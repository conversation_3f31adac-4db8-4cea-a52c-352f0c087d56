<template>
  <!-- 主容器 -->
  <div class="user-video-page">
    <!-- 页面头部区域 -->
    <div class="page-header">
      <h1>蜗牛AI教育 - 视频学习</h1>
      <p>选择您感兴趣的课程开始学习</p>
    </div>
    <!-- 筛选区域卡片 -->
    <div class="filter-section">
      <a-card title="课程筛选" :bordered="false">
        <!-- 多级联动筛选行 -->
        <div class="filter-row">
          <!-- 课程选择 -->
          <div class="filter-item">
            <a-select
                v-model:value="selectedCourse"
                placeholder="选择课程"
                @change="onCourseChange"
                allow-clear
            >
              <a-select-option
                  v-for="course in courses"
                  :key="course.courseId"
                  :value="course.courseId"
              >
                {{ course.courseName }}
              </a-select-option>
            </a-select>
          </div>
          <!-- 模块选择（依赖课程） -->
          <div class="filter-item">
            <a-select
                v-model:value="selectedModule"
                placeholder="选择模块"
                @change="onModuleChange"
                :disabled="!selectedCourse"
                allow-clear
            >
              <a-select-option
                  v-for="module in modules"
                  :key="module.moduleId"
                  :value="module.moduleId"
              >
                {{ module.moduleName }}
              </a-select-option>
            </a-select>
          </div>
          <!-- 章节选择（依赖模块） -->
          <div class="filter-item">
            <a-select
                v-model:value="selectedChapter"
                placeholder="选择章节"
                @change="onChapterChange"
                :disabled="!selectedModule"
                allow-clear
            >
              <a-select-option
                  v-for="chapter in chapters"
                  :key="chapter.chapterId"
                  :value="chapter.chapterId"
              >
                {{ chapter.chapterName }}
              </a-select-option>
            </a-select>
          </div>
        </div>
        <!-- 搜索和重置行 -->
        <div class="search-row">
          <div class="search-input">
            <!-- 关键词搜索输入框 -->
            <a-input
                v-model:value="searchKeyword"
                placeholder="搜索视频标题"
                @pressEnter="searchVideos"
            >
              <template #suffix>
                <a-button type="text" @click="searchVideos">
                  <SearchOutlined/>
                </a-button>
              </template>
            </a-input>
          </div>
          <div class="search-actions">
            <!-- 重置筛选按钮 -->
            <a-button @click="resetFilters">重置筛选</a-button>
          </div>
        </div>
      </a-card>
    </div>
    <!-- 视频列表区域 -->
    <div class="video-section">
      <!-- 加载状态指示器 -->
      <a-spin :spinning="loading">
        <!-- 视频内容容器 -->
        <div class="video-content-container">
          <!-- 视频卡片网格布局 -->
          <div class="video-grid">
          <!-- 单个视频卡片 -->
          <a-card
              v-for="video in videos"
              :key="video.videoId"
              hoverable
              class="video-card"
              @click="playVideo(video)"
          >
            <!-- 视频封面 -->
            <template #cover>
              <div class="video-cover">
                <img
                    :src="video.videoCover || defaultCoverImage"
                    :alt="video.videoTitle"
                    style="width: 100%; height: 180px; object-fit: cover"
                    @error="handleImageError"
                />
                <!-- 视频时长标签 -->
                <div class="video-duration">
                  {{ formatDuration(video.videoDuration) }}
                </div>
                <!-- 播放按钮遮罩 -->
                <div class="play-overlay">
                  <PlayCircleOutlined style="font-size: 48px; color: white"/>
                </div>
              </div>
            </template>
            <!-- 视频元信息 -->
            <a-card-meta
                :title="video.videoTitle"
            />
          </a-card>
          </div>

          <!-- 空状态提示 -->
          <a-empty v-if="!loading && videos.length === 0" description="暂无视频"/>

          <!-- 分页控件 -->
          <div v-if="videos.length > 0" style="text-align: center; margin-top: 24px">
            <a-pagination
                v-model:current="currentPage"
                v-model:page-size="pageSize"
                :total="total"
                show-size-changer
                show-quick-jumper
                :page-size-options="['5', '10', '20', '50']"
                :show-total="(total:any, range:any) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
                @change="onPageChange"
                @showSizeChange="onPageSizeChange"
            />
          </div>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, computed} from 'vue'
import {useRouter} from 'vue-router'
import {message} from 'ant-design-vue'
import {
  SearchOutlined,
  PlayCircleOutlined
} from '@ant-design/icons-vue'
import {getAllVideo} from "@/api/videoController.ts";
import {getAllVideoCourse} from "@/api/videoCourseController.ts";
import {getAllVideoModule} from "@/api/videoModuleController.ts";
import {getAllVideoChapter} from "@/api/videoChapterController.ts";
import {getModuleByCourseId} from "@/api/courseModuleMappingController.ts";
import {getChapterByModuleId} from "@/api/moduleChapterMappingController.ts";
import {getVideosByCondition, getVideosByConditionWithPage} from "@/api/videoQueryController.ts";

/**
 * 数据类型定义
 */

// 课程数据结构
interface Course {
  courseId: number
  courseName: string
  courseAiSummary?: string
}

// 模块数据结构
interface Module {
  moduleId: number
  moduleName: string
  moduleAiSummary?: string
}

// 章节数据结构
interface Chapter {
  chapterId: number
  chapterName: string
  chapterAiSummary?: string
}

// 视频数据结构
interface Video {
  videoId: number
  uploaderId: number
  videoTitle: string
  videoUrl: string
  originalVideoName: string
  videoCover: string
  videoDuration: number
  videoStatus: number
  createTime: string
  updateTime: string
  textData?: any[]
  summaryData?: any[]
  summary100?: string
}

// AI视频总结数据结构
interface VideoSummary {
  ai_video_summary_id: number
  video_id: number
  audio_id?: number
  start_seconds: number
  end_seconds: number
  summary_content: string
}

/**
 * 路由和响应式数据
 */
const router = useRouter()
const loading = ref(false) // 加载状态
const courses = ref<Course[]>([]) // 课程列表
const modules = ref<Module[]>([]) // 模块列表
const chapters = ref<Chapter[]>([]) // 章节列表
const videos = ref<Video[]>([]) // 视频列表

/**
 * 筛选条件
 */
const selectedCourse = ref<number | undefined>() // 当前选中的课程ID
const selectedModule = ref<number | undefined>() // 当前选中的模块ID
const selectedChapter = ref<number | undefined>() // 当前选中的章节ID
const searchKeyword = ref('') // 搜索关键词

/**
 * 分页配置
 */
const currentPage = ref(1) // 当前页码
const pageSize = ref(20) // 每页显示数量
const total = ref(0) // 总数据量

// 默认封面图片
const defaultCoverImage = ref('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMwMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMjAgMTEwTDE2MCA5MEwxMjAgNzBWMTEwWiIgZmlsbD0iIzhDODk4QyIvPgo8dGV4dCB4PSIxNTAiIHk9IjEzNSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE0IiBmaWxsPSIjOEM4OThDIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7op4bpopHlsIHpnaI8L3RleHQ+Cjwvc3ZnPgo=')


/**
 * 数据加载方法
 */

// 加载课程数据
const loadCourses = async () => {
  try {
    const response = await getAllVideoCourse()
    if (response.data.code === 0) {
      courses.value = response.data.data as Course[]
    }
  } catch (error) {
    console.error('加载课程失败:', error)
  }
}

// 加载模块数据（根据课程ID）
const loadModules = async (courseId: number) => {
  try {
    const response = await getModuleByCourseId({courseId})
    if (response.data.code === 0) {
      const allModules = response.data.data as Module[]
      console.log('所有模块：', allModules)
      modules.value = allModules as Module[]
    } else {
      modules.value = []
    }
  } catch (error) {
    console.error('加载模块失败:', error)
    modules.value = []
  }
}

// 加载章节数据（根据模块ID）
const loadChapters = async (moduleId: number) => {
  try {
    const response = await getChapterByModuleId({moduleId})
    if (response.data.code === 0) {
      const allChapters = response.data.data as Chapter[]

      chapters.value = allChapters as Chapter[]
    } else {
      chapters.value = []
    }
  } catch (error) {
    console.error('加载章节失败:', error)
    chapters.value = []
  }
}


// 加载视频数据（应用筛选条件）
const loadVideos = async () => {
  loading.value = true
  try {
    // 构建请求参数
    const params: API.getVideosByConditionWithPageParams = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    }
    if (selectedCourse.value !== null && selectedCourse.value !== undefined) {
      params.courseId = selectedCourse.value
    }
    if (selectedModule.value !== null && selectedModule.value !== undefined) {
      params.moduleId = selectedModule.value
    }
    if (selectedChapter.value !== null && selectedChapter.value !== undefined) {
      params.chapterId = selectedChapter.value
    }
    const response = await getVideosByConditionWithPage(params)
    let allVideos: Video[] = []

    if (response.data.code === 0) {
      // allVideos = response.data.data as Video[]
      allVideos = response.data.data?.records as Video[]
      // const pageData = response.data.data?.records
      videos.value = allVideos  // 分页数据列表
      total.value = response.data.data?.total as number// 总条数
    } else {
      console.error('获取视频列表失败')
    }
    if (searchKeyword.value) {
      allVideos = allVideos.filter(v =>
          v.videoTitle.toLowerCase().includes(searchKeyword.value.toLowerCase())
      )
    }
    videos.value = allVideos
    total.value = allVideos.length
  } catch (error) {
    console.error('加载视频失败:', error)
    videos.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}


/**
 * 筛选事件处理
 */

// 课程选择变化事件
const onCourseChange = (value: number) => {
  selectedModule.value = undefined
  selectedChapter.value = undefined
  modules.value = []
  chapters.value = []

  if (value) {
    loadModules(value)
  }
  loadVideos()
}

// 模块选择变化事件
const onModuleChange = (value: number) => {
  selectedChapter.value = undefined
  chapters.value = []

  if (value) {
    loadChapters(value)
  }
  loadVideos()
}

// 章节选择变化事件
const onChapterChange = () => {
  loadVideos()
}

// 搜索视频
const searchVideos = () => {
  currentPage.value = 1
  loadVideos()
}

// 重置所有筛选条件
const resetFilters = () => {
  selectedCourse.value = undefined
  selectedModule.value = undefined
  selectedChapter.value = undefined
  searchKeyword.value = ''
  modules.value = []
  chapters.value = []
  currentPage.value = 1
  loadVideos()
}

/**
 * 分页处理
 */

// 页码变化事件
const onPageChange = (page: number, size: number) => {
  currentPage.value = page
  pageSize.value = size
  loadVideos()
}

// 每页数量变化事件
const onPageSizeChange = (current: number, size: number) => {
  currentPage.value = 1
  pageSize.value = size
  loadVideos()
}

/**
 * 视频播放相关方法
 */

// 播放视频
const playVideo = (video: Video) => {
  // 跳转到专门的视频播放页面
  router.push(`/video/${video.videoId}`)
}

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = defaultCoverImage.value
}


/**
 * 工具函数
 */

// 格式化视频时长（秒→HH:MM:SS）
const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

// 格式化日期
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}


// 初始化数据
onMounted(() => {
  loadCourses()
  loadVideos()
})
</script>

<style scoped>
/* 主容器样式 */
.user-video-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部样式 */
.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-header h1 {
  color: #1890ff;
  margin-bottom: 8px;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 24px;
}

.filter-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.filter-item {
  flex: 1;
  min-width: 200px;
}

.filter-item .ant-select {
  width: 100%;
}

.search-row {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 300px;
}

.search-actions {
  flex-shrink: 0;
}

/* 视频列表区域样式 */
.video-section {
  background: white;
  padding: 24px;
  border-radius: 8px;
}

/* 视频内容容器 - 添加滚动条 */
.video-content-container {
  max-height: 600px;
  overflow-y: auto;
  padding-right: 8px;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  justify-items: center;
}

.video-grid .video-card {
  width: 100%;
  max-width: 320px;
}

/* 视频卡片样式 */
.video-card {
  transition: all 0.3s;
  cursor: pointer;
}

.video-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

/* 视频封面容器 */
.video-cover {
  position: relative;
  overflow: hidden;
}

/* 视频时长标签 */
.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

/* 播放按钮遮罩 */
.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.3s;
}

.video-card:hover .play-overlay {
  opacity: 1;
}

/* 响应式样式 */
@media (max-width: 1200px) {
  .video-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  }
}

@media (max-width: 768px) {
  .video-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
  }

  .filter-row {
    flex-direction: column;
  }

  .filter-item {
    min-width: auto;
  }

  .search-row {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .video-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}
</style>