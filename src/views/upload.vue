<template>
  <div class="upload-container">
    <h2>MinIO 分片上传测试</h2>

    <input
      type="file"
      @change="onFileSelect"
      :disabled="uploading"
      ref="fileInput"
    />

    <!-- 上传按钮 -->
    <div v-if="file" class="info">
      <p>文件名: {{ file.name }}</p>
      <p>大小: {{ formatSize(file.size) }}</p>
      <p>MD5: {{ fileHash || '计算中...' }}</p>
      <p v-if="uploadResult">上传成功！<a :href="uploadResult" target="_blank">预览</a></p>
    </div>

    <!-- 上传进度 -->
    <div v-if="progress.total > 0" class="progress">
      <p>上传进度: {{ Math.round(progress.percent * 100) }}%</p>
      <div class="bar">
        <div
          class="fill"
          :style="{ width: progress.percent * 100 + '%' }"
        ></div>
      </div>
      <p>已上传 {{ progress.uploaded }} / {{ progress.total }} 个分片</p>
    </div>

    <!-- 按钮 -->
    <div class="actions">
      <button
        v-if="file && !uploading"
        @click="startUpload"
        :disabled="!fileHash"
      >
        {{ isResumable ? '继续上传' : '开始上传' }}
      </button>

      <button v-if="uploading" @click="pauseUpload">暂停上传</button>
      <button @click="reset">重新选择</button>
    </div>

    <p v-if="error" class="error">{{ error }}</p>
  </div>
</template>

<script>
import SparkMD5 from 'spark-md5';

export default {
  name: 'MinIOUpload',
  data() {
    return {
      file: null,         // 选中的文件
      fileHash: '',       // 文件整体 MD5
      uploadId: '',       // 分片上传任务 ID
      chunkSize: 5 * 1024 * 1024, // 每个分片 5MB
      uploading: false,
      paused: false,
      progress: {
        uploaded: 0,
        total: 0,
        percent: 0,
        done: false
      },
      uploadResult: '',   // 最终文件 URL
      error: ''
    };
  },

  computed: {
    // 是否是断点续传（已有 uploadId）
    isResumable() {
      return !!this.uploadId;
    }
  },

  methods: {
    reset() {
      this.$refs.fileInput.value = '';
      this.file = null;
      this.fileHash = '';
      this.uploadId = '';
      this.progress = { uploaded: 0, total: 0, percent: 0 };
      this.uploadResult = '';
      this.error = '';
      this.paused = false;
      this.uploading = false;
    },

    onFileSelect(e) {
      const file = e.target.files[0];
      if (!file) return;

      this.reset();
      this.file = file;

      // 第一步：计算文件 MD5
      this.calculateFileHash();
    },

    // 格式化文件大小为 KB/MB/GB
    formatSize(bytes) {
      if (bytes === 0) return '0B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i];
    },

    // 计算文件整体 MD5
    calculateFileHash() {
      this.error = '';
      const file = this.file;
      const chunkSize = 2 * 1024 * 1024; // 每次读 2MB 计算 MD5
      const spark = new SparkMD5.ArrayBuffer();
      const fileReader = new FileReader();

      let currentChunk = 0;

      const loadNext = () => {
        const start = currentChunk * chunkSize;
        const end = start + chunkSize > file.size ? file.size : start + chunkSize;
        const chunk = file.slice(start, end);

        fileReader.onload = (e) => {
          spark.append(e.target.result);
          currentChunk++;

          // 更新 UI
          this.fileHash = `计算中... (${Math.round((start / file.size) * 100)}%)`;

          if (currentChunk < Math.ceil(file.size / chunkSize)) {
            loadNext();
          } else {
            this.fileHash = spark.end();
            console.log('文件 MD5:', this.fileHash);
            // 计算完 MD5 后，检查是否秒传
            this.checkExist();
          }
        };

        fileReader.readAsArrayBuffer(chunk);
      };

      loadNext();
    },

    // 检查文件是否已存在（秒传）
    async checkExist() {
      try {
        const res = await fetch('http://localhost:8080/api/file/check-exist?fileHash=' + this.fileHash);
        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }
        const data = await res.json();

        if (data.exists) {
          this.uploadResult = data.url;
          this.error = '';
          alert('✅ 秒传命中！文件已存在，无需上传。');
        } else {
          console.log('文件未上传过，准备分片上传');
        }
      } catch (err) {
        console.error('检查文件存在性失败:', err);
      }
    },

    // 开始上传（或继续上传）
    async startUpload() {
      this.paused = false;
      this.uploading = true;
      this.error = '';

      // 如果没有 uploadId，先初始化上传任务
      if (!this.uploadId) {
        await this.initUpload();
      }

      // 开始分片上传
      await this.uploadChunks();
    },

    // 初始化上传任务
    async initUpload() {
      try {
        const res = await fetch('http://localhost:8080/api/file/chunk/start', {
          method: 'POST',
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          body: new URLSearchParams({
            fileName: this.file.name,
            fileHash: this.fileHash,
            totalChunks: Math.ceil(this.file.size / this.chunkSize)
          })
        });

        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }

        const data = await res.json();
        this.uploadId = data.uploadId;
        console.log('初始化上传成功，uploadId:', this.uploadId);
      } catch (err) {
        this.handleError('初始化上传失败: ' + err.message);
      }
    },

    // 上传所有分片
    async uploadChunks() {
      const chunks = this.createFileChunks();
      this.progress.total = chunks.length;

      for (let i = 0; i < chunks.length; i++) {
        if (this.paused || !this.uploading) return;

        // 查询进度，跳过已上传的
        try {
          const totalChunks = Math.ceil(this.file.size / this.chunkSize);
          const progressRes = await fetch(`http://localhost:8080/api/file/chunk/progress?uploadId=${encodeURIComponent(this.uploadId)}&totalChunks=${totalChunks}`);
          if (!progressRes.ok) {
            throw new Error(`HTTP error! status: ${progressRes.status}`);
          }
          const progressData = await progressRes.json();
          this.progress = progressData;

          if (progressData.done) {
            this.uploading = false;
            this.uploadResult = await this.getMergeResult();
            return;
          }

          if (progressData.uploaded > i) continue; // 已上传，跳过

          // 上传分片
          const formData = new FormData();
          formData.append('uploadId', this.uploadId);
          formData.append('chunkIndex', i);
          formData.append('chunk', chunks[i].blob);
          formData.append('fileHash', this.fileHash); // 可选：用于校验

          const res = await fetch('http://localhost:8080/api/file/chunk/upload', {
            method: 'POST',
            body: formData
          });

          if (res.ok) {
            this.progress.uploaded = i + 1;
            this.progress.percent = (i + 1) / chunks.length;
          } else {
            throw new Error(await res.text());
          }
        } catch (err) {
          this.handleError(`分片 ${i} 上传失败: ${err.message}`);
          return;
        }
      }

      // 所有分片上传完成，合并
      this.mergeChunks();
    },

    // 创建文件分片
    createFileChunks() {
      const chunks = [];
      let start = 0;

      while (start < this.file.size) {
        const end = Math.min(start + this.chunkSize, this.file.size);
        const blob = this.file.slice(start, end);
        chunks.push({ index: start / this.chunkSize, blob });
        start = end;
      }

      return chunks;
    },

    // 合并分片
    async mergeChunks() {
      try {
        const res = await fetch('http://localhost:8080/api/file/chunk/merge', {
          method: 'POST',
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          body: new URLSearchParams({
            uploadId: this.uploadId,
            fileName: this.file.name,
            fileHash: this.fileHash
          })
        });

        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }

        const path = await res.text();
        const urlRes = await fetch('http://localhost:8080/api/file/content?fileName=' + encodeURIComponent(path));
        this.uploadResult = await urlRes.text();
        alert('🎉 上传成功！');
      } catch (err) {
        this.handleError('合并失败: ' + err.message);
      } finally {
        this.uploading = false;
      }
    },

    // 获取合并结果（如果已合并）
    async getMergeResult() {
      // 这里可以调用你的预览接口
      const res = await fetch('http://localhost:8080/api/file/content?fileName=your-final-path');
      return await res.text();
    },

    pauseUpload() {
      this.paused = true;
      this.uploading = false;
      alert('上传已暂停，可稍后继续');
    },

    handleError(msg) {
      this.error = msg;
      this.uploading = false;
      console.error(msg);
    }
  }
};
</script>

<style scoped>
.upload-container {
  max-width: 600px;
  margin: 40px auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.info, .progress, .actions {
  margin: 15px 0;
}

.progress .bar {
  width: 100%;
  height: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  overflow: hidden;
}

.progress .fill {
  height: 100%;
  background-color: #4caf50;
  transition: width 0.3s;
}

button {
  margin-right: 10px;
  padding: 8px 16px;
  font-size: 14px;
}

.error {
  color: red;
}
</style>